package cn.medsci.portal.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.medsci.auth.core.dto.userauth.GetMedsciUserFrontLoginResponse;
import cn.medsci.center.commom.base.module.ModuleEnum;
import cn.medsci.center.commom.util.EmptyUtils;
import cn.medsci.center.commom.util.HandleIdUtil;
import cn.medsci.center.commom.util.UrlUtil;
import cn.medsci.center.commom.vo.ObjectRestPageResponse;
import cn.medsci.center.commom.vo.ObjectRestResponse;
import cn.medsci.center.mgr.api.rest.ILinkInfoRest;
import cn.medsci.center.mgr.api.rest.ILinkVisitLogRest;
import cn.medsci.center.search.api.dto.estoolimpactfactor.GetToolImpactFactorPageRequest;
import cn.medsci.center.search.api.dto.estoolimpactfactor.GetToolImpactFactorPageResponse;
import cn.medsci.center.search.api.dto.medscisearch.GetMedsciSiteRelevantSearchPageRequest;
import cn.medsci.center.search.api.dto.medscisearch.GetMedsciSiteRelevantSearchPageResponse;
import cn.medsci.center.search.api.dto.medscisearch.GetMedsciSiteSearchPageRequest;
import cn.medsci.center.search.api.dto.medscisearch.GetMedsciSiteSearchPageResponse;
import cn.medsci.center.search.api.dto.usersearchhistory.SaveUserSearchHistoryRequest;
import cn.medsci.center.search.api.rest.IEsToolImpactFactor;
import cn.medsci.center.search.api.rest.IMedsciSearchRest;
import cn.medsci.center.tag.api.dto.tag.GetTagDetailRequest;
import cn.medsci.center.tag.api.dto.tag.GetTagDetailResponse;
import cn.medsci.center.tag.api.rest.ITagRest;
import cn.medsci.portal.asynctask.AsyncTask;
import cn.medsci.portal.dto.GetWebMedsciSiteSearchPageRequest;
import cn.medsci.portal.dto.search.GetWebSiteRelevantSearchPageResponse;
import cn.medsci.portal.dto.toolimpactfactor.GetPortalToolImpactFactorPageResponse;
import cn.medsci.portal.enums.SourceOsEnum;
import cn.medsci.portal.service.CommonService;
import cn.medsci.portal.service.PortalSearchService;
import cn.medsci.portal.service.PortalToolImpactFactorService;
import cn.medsci.portal.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * @program: medsci-scale
 * @description:
 * @author: Tao.Wu
 * @create: 2021-01-15 13:45
 **/
@Service
@Slf4j
public class PortalSearchServiceImpl implements PortalSearchService {

    @Autowired
    private IMedsciSearchRest iMedsciSearchRest;

    @Autowired
    private ILinkInfoRest iLinkInfoRest;

    @Autowired
    private ILinkVisitLogRest iLinkVisitLogRest;

    @Autowired
    private ITagRest iTagRest;

    @Autowired
    private IEsToolImpactFactor impactFactorRest;

    @Autowired
    private PortalToolImpactFactorService portalToolImpactFactorService;

    @Autowired
    private CommonService commonService;

    private final AsyncTask asyncTask;

    public PortalSearchServiceImpl(AsyncTask asyncTask) {
        this.asyncTask = asyncTask;
    }


    @Override
    public String search(GetWebMedsciSiteSearchPageRequest bean, Model model) {
        return searchDefault(bean,model);
        //根据不同的module实现不同查询
//        if ("tool_impact_factor".equals(bean.getModule())) {
//            return searchJournal(bean,model);
//        } else {
//            return searchDefault(bean,model);
//        }
    }


    public int delPageIndex(int pageSize,int maxTotalSize,Integer pageIndex){
        if (null == pageIndex||0 == pageIndex) {
            pageIndex=1;
        }
        int maxIndex=maxTotalSize/pageSize;
        if (pageIndex > maxIndex) {
            return maxIndex;
        }
        return pageIndex;
    }

    /**
     * 期刊站内搜索
     * @param req
     * @param model
     * @return
     */
    public String searchJournal(GetWebMedsciSiteSearchPageRequest req,Model model){
        int pageSize=10;
        int maxTotalSize=50;
        int pageIndex = delPageIndex(pageSize,maxTotalSize,req.getPage());

        GetToolImpactFactorPageRequest bean=new GetToolImpactFactorPageRequest();
        bean.setProjectId(1L);
        bean.setPageSize(pageSize);
        bean.setPageIndex(pageIndex-1);
        bean.setSource(1);
        bean.setOldNames(req.getQ());
        //设置高亮
        bean.setIsHighlight(1);
        //处理期刊tag搜索
        if (EmptyUtils.isNotEmpty(req.getTag())){
            bean.setTagName(getTagId(req.getTag()));
        }

        //期刊搜索值特殊处理
        req.setQ(cn.medsci.center.commom.util.StringUtil.getReplaceEsImpactFactorName(req.getQ()));
        bean.setNames(req.getQ());
        GetMedsciUserFrontLoginResponse loginUserInfo = CookieUtils.getLoginUserInfo();
        // 用户记录用户搜索日志
        if (loginUserInfo != null && StrUtil.isNotEmpty(bean.getOldNames())){
            SaveUserSearchHistoryRequest saveUserSearchHistoryRequest = new SaveUserSearchHistoryRequest();
            BeanUtils.copyProperties(bean,saveUserSearchHistoryRequest);
            saveUserSearchHistoryRequest.setSourceFrom(SourceOsEnum.PC.getSourceName());
            String ipAddr = RequestUtil.getIpAddr();
            saveUserSearchHistoryRequest.setIp(ipAddr);
            saveUserSearchHistoryRequest.setCreatedBy(HandleIdUtil.decode_user_id_toLong(loginUserInfo.getUserId())).setCreatedName(loginUserInfo.getUserName());
            saveUserSearchHistoryRequest.setUpdatedBy(HandleIdUtil.decode_user_id_toLong(loginUserInfo.getUserId())).setUpdatedName(loginUserInfo.getUserName());
            asyncTask.saveUserSearchHistoryTask(saveUserSearchHistoryRequest);
        }
        ObjectRestPageResponse<List<GetPortalToolImpactFactorPageResponse>> portalImpactFactorPage=new ObjectRestPageResponse<>();
        try {
            //调用期刊搜索

            ObjectRestPageResponse<List<GetToolImpactFactorPageResponse>> impactFactorPage=impactFactorRest.getToolImpactFactorPage(bean);
            portalImpactFactorPage=portalToolImpactFactorService.getToolImpactFactorPage(impactFactorPage);
            Long totalSize=impactFactorPage.getTotalSize()<maxTotalSize?impactFactorPage.getTotalSize():maxTotalSize;
            model.addAttribute("totalSize",totalSize);
            PageTag pageTag=new PageTag(new PageBean(bean.getPageIndex(),
                    pageSize,Integer.valueOf(totalSize.toString()),"/sci/index.do"),bean,null,1);
            model.addAttribute("PageTag", pageTag.convertHTML());
        } catch (Exception e) {
            log.error("调用期刊搜索异常--->{}",e);
        }

        try {
            GetMedsciSiteRelevantSearchPageRequest request=new GetMedsciSiteRelevantSearchPageRequest();
            request.setContent(req.getQ());
            ObjectRestPageResponse<List<GetMedsciSiteRelevantSearchPageResponse>> response3=iMedsciSearchRest.getMedsciSiteRelevantSearchPage(request);
            List<GetWebSiteRelevantSearchPageResponse> response1=getWebSiteRelevantSearchPageResponses(response3.getData());
            model.addAttribute("siteRelevantSearchData", response1);
        } catch (Exception e) {
            log.error("调用相关搜索异常--->{}",e);
        }
        String tagUrl=UrlUtil.removeUrlParameter(StringUtil.getRequestURI(),"tag");
        if (!tagUrl.contains("?")){
            tagUrl+="?page=1";
        }
        model.addAttribute("tagUrl", tagUrl);
        model.addAttribute("paperPageData", portalImpactFactorPage.getData());
        model.addAttribute("paperPagePamData", req);
        model.addAttribute("httpUrl", StringUtil.getHttpServletRequestUrl());
        model.addAttribute("response",portalImpactFactorPage);
        return "medsciSearchForJournal";
    }



    /**
     * 获取tagId
     * @param tagName
     * @return
     */
    public String getTagId(String tagName){
        try {
            GetTagDetailRequest request1=new GetTagDetailRequest();
            request1.setTagName(tagName);
            ObjectRestResponse<GetTagDetailResponse> tagDetail=iTagRest.getTagDetail(request1);
            if (EmptyUtils.isNotEmpty(tagDetail.getData())){
                return String.valueOf(tagDetail.getData().getId());
            }
        } catch (Exception e) {
            return "";
        }
        return "";
    }

    /**
     * 主站默认搜索
     * @param bean
     * @param model
     * @return
     */
    public String searchDefault(GetWebMedsciSiteSearchPageRequest bean, Model model){
        //外链调用设置初始值
        if (EmptyUtils.isEmpty(bean.getModule())) {
            bean.setModule("");
        }
        if (EmptyUtils.isEmpty(bean.getQ())){
            bean.setQ("");
        }else{
            bean.setQ(URLDecoder.decode(bean.getQ(), StandardCharsets.UTF_8));
        }
        if (EmptyUtils.isEmpty(bean.getTimeType())) {
            bean.setTimeType(0);
        }
        if (EmptyUtils.isEmpty(bean.getSortType())) {
            bean.setSortType(2);
        }
        if (EmptyUtils.isEmpty(bean.getSearchType())) {
            bean.setSearchType(1);
        }
        int size=15;
        int totalSize=500;
        int maxIndex=totalSize % size == 0 ? totalSize / size : (totalSize / size) + 1;
        if ((bean.getPage()+1) > maxIndex) {
            model.addAttribute("message", "最多500条,查看更多数据请联系管理人员");
        }
        GetMedsciSiteSearchPageRequest pageRequest = new GetMedsciSiteSearchPageRequest();
        pageRequest.setPageSize(size);
        //-- 处理es的PageIndex
        if (bean.getPage()>0){
            bean.setPage(bean.getPage()-1);
        }else {
            bean.setPage(0);
        }
        GetMedsciSiteRelevantSearchPageRequest request=new GetMedsciSiteRelevantSearchPageRequest();
        request.setContent(bean.getQ());
        ObjectRestPageResponse<List<GetMedsciSiteRelevantSearchPageResponse>> response3=iMedsciSearchRest.getMedsciSiteRelevantSearchPage(request);
        //103：存在敏感词
        if (response3.getStatus() == 103){
            pageRequest.setContent("");
            bean.setId("");
        }
        List<GetWebSiteRelevantSearchPageResponse> response1=getWebSiteRelevantSearchPageResponses(response3.getData());
        model.addAttribute("siteRelevantSearchData", response1);

        BeanUtils.copyProperties(bean, pageRequest);
        pageRequest.setContent(bean.getQ());
        pageRequest.setPageIndex(bean.getPage());
        GetMedsciUserFrontLoginResponse loginUserInfo = CookieUtils.getLoginUserInfo();
        // 用户记录用户搜索日志
        if (loginUserInfo != null && StrUtil.isNotEmpty(pageRequest.getContent())){
            SaveUserSearchHistoryRequest saveUserSearchHistoryRequest = new SaveUserSearchHistoryRequest();
            BeanUtils.copyProperties(pageRequest,saveUserSearchHistoryRequest);
            saveUserSearchHistoryRequest.setSourceFrom(SourceOsEnum.PC.getSourceName());
            saveUserSearchHistoryRequest.setProjectId(1L);
            String ipAddr = RequestUtil.getIpAddr();
            saveUserSearchHistoryRequest.setIp(ipAddr);
            saveUserSearchHistoryRequest.setCreatedBy(HandleIdUtil.decode_user_id_toLong(loginUserInfo.getUserId())).setCreatedName(loginUserInfo.getUserName());
            saveUserSearchHistoryRequest.setUpdatedBy(HandleIdUtil.decode_user_id_toLong(loginUserInfo.getUserId())).setUpdatedName(loginUserInfo.getUserName());
            pageRequest.setMobile(loginUserInfo.getMobile());
            asyncTask.saveUserSearchHistoryTask(saveUserSearchHistoryRequest);
        }
        ObjectRestPageResponse<List<GetMedsciSiteSearchPageResponse>> response2=iMedsciSearchRest.getMedsciSiteSearchPage(pageRequest);
        ObjectRestPageResponse<List<cn.medsci.portal.dto.common.GetMedsciSiteSearchPageResponse>> response=commonService.getMedsciSiteSearchPage(response2);
        Long aa=response.getTotalSize();
        if (Long.valueOf(aa).intValue() < 500) {
            totalSize=Long.valueOf(aa).intValue();
        }
        //处理分页的PageIndex
        if (bean.getPage()>= 0) {
            bean.setPage(bean.getPage()+1);
        }
        PageTag pageTag=new PageTag(new PageBean(bean.getPage(),
                size,totalSize,"/search"),bean,1);
        model.addAttribute("PageTag", pageTag.convertHTML());
        model.addAttribute("paperPageData", response.getData());
        model.addAttribute("paperPagePamData", pageRequest);
        model.addAttribute("totalSize",totalSize);
        model.addAttribute("httpUrl", StringUtil.getHttpServletRequestUrl());
        model.addAttribute("page", bean.getPage()==0?1:bean.getPage());
        if (EmptyUtils.isNotEmpty(pageRequest.getContent())){
            try {
                String encodeQ= URLEncoder.encode(pageRequest.getContent(),"UTF-8");
                model.addAttribute("encodeQ",encodeQ);
                model.addAttribute("encodeW",pageRequest.getContent());

            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        return "medsciSearch";
    }


    /**
     * 处理相关搜索数据-编码内容
     * @param responses
     * @return
     */
    public List<GetWebSiteRelevantSearchPageResponse> getWebSiteRelevantSearchPageResponses(List<GetMedsciSiteRelevantSearchPageResponse> responses){
        List<GetWebSiteRelevantSearchPageResponse> responses1=new ArrayList<>();
        if (CollectionUtils.isNotEmpty(responses)){
            for (GetMedsciSiteRelevantSearchPageResponse response:responses){
                GetWebSiteRelevantSearchPageResponse response1=new GetWebSiteRelevantSearchPageResponse();
                BeanUtils.copyProperties(response,response1);
                if (EmptyUtils.isNotEmpty(response.getContent())){
                    try {
                        response1.setUrlContent(URLEncoder.encode(response.getContent(),"UTF-8"));
                    } catch (UnsupportedEncodingException e) {
                        e.printStackTrace();
                    }
                }
                responses1.add(response1);
            }
        }
        return responses1;
    }
}
