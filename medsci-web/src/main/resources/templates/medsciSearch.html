<!DOCTYPE HTML>
<!-- BEGIN html -->
<html xmlns:th="http://www.thymeleaf.org" xmlns:thImg="http://www.w3.org/1999/xhtml">
	<!-- BEGIN head -->
	<head>
		<!-- Meta Tags -->
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" >
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="viewport" content="width=device-width,initial-scale=1.0, maximum-scale=1">
        <meta http-equiv="Cache-Control" content="no-siteapp">
        <title th:text="${not #strings.isEmpty(paperPagePamData.content) ? (totalSize > 0 ? paperPagePamData.content+'相关资讯-临床研究进展，文献-MedSci.cn' : '搜索无结果-MedSci.cn') : '临床研究进展，文献-MedSci.cn'}"></title>
        <!-- Favicon -->
		<link rel="shortcut icon" href="https://static.medsci.cn/product/medsci-site/portal/favicon.ico" type="image/x-icon" />
        <!-- Stylesheets -->
        <link rel="stylesheet" href="https://static.medsci.cn/product/medsci-site/portal/css/reset.min.css" />
        <link rel="stylesheet" href="https://static.medsci.cn/product/medsci-site/portal/css/bootstrap.min.css" />
        <link rel="stylesheet" href="https://static.medsci.cn/product/medsci-site/portal/css/owl.carousel.css" />
        <link rel="stylesheet" href="https://static.medsci.cn/product/medsci-site/portal/css/main-stylesheet.min.css?date=202506231200" />
        <link rel="stylesheet" href="https://static.medsci.cn/product/medsci-site/portal/css/shortcodes.min.css" />
        <link rel="stylesheet" href="https://static.medsci.cn/product/medsci-site/portal/css/animate.css" />
        <link rel="stylesheet" href="https://static.medsci.cn/product/medsci-site/portal/css/responsive.min.css" />
        <link type="text/css" rel="stylesheet" href="https://img.medsci.cn/web/prod/css/login.min.css?date=1615450612522" />
        <link type="text/css" rel="stylesheet" href="https://img.medsci.cn/web/prod/css/journal.min.css" />
        <link type="text/css" rel="stylesheet" href="https://img.medsci.cn/web/prod/css/iconfont.css" />

        <style>
            #searchContent{
                padding-top: 60px;
            }
            #searchContent .item-header{
                height: auto;
            }
            #searchContent .select-area select{
                background: #FFFFFF!important;
            }
            #searchContent .qikan-result-mshow{
                display: none;
            }
            #searchContent .qikan-add{
                display: none;
            }
            #searchContent .qikan-result-mobile{
                display: none;
            }
            @media screen and (max-width: 768px) {
                #searchHead .search-wrap{
                    display: none;
                }
                #searchContent #main-menu strong a:before{
                    content: initial;
                }
                #searchContent .select-area{
                    display: flex;
                }
                #searchContent .select-area select{
                    padding: 7px;
                }
                #searchContent #timeType{
                    margin-right: 10px;
                }
                #searchContent #sortType{
                    margin-right: 10px;
                }
                #searchContent .related-search-mobile {
                    display: none;
                }
                #searchContent .item-header{
                    height: initial;
                }
                #searchContent .qikan-result-mobile{
                    display: block;
                }
                #searchContent .medsci-search{
                    margin-bottom: 5px;
                }
            }
        </style>

        <!-- END head -->
	

</head>

	<!-- BEGIN body -->
	<!-- <body> -->
	<body class="ot-menu-will-follow">

		<!-- BEGIN .boxed -->
		<div class="boxed" id="searchContent">

            <!-- BEGIN .header 导航 -->
            <div class="header ms-header-media" th:insert="~{commonPages/header::header}"></div>

			<!-- BEGIN .content -->
			<div class="content">

				<!-- BEGIN .wrapper -->
				<div class="wrapper">

                    <div class="journal-index medsci-search">
                        <form action="#" onsubmit="return false" method="get" class="contact-form-content">
                            <label class="journal-input">
                                <input th:value="${paperPagePamData.content}" id="keyword" type="text" value="" placeholder="请输入关键词搜索" />
                                <button onclick="searchSite()" type="button" class="search-button"><i class="iconfont icon-search"></i> 搜索</button>
                            </label>
                        </form>
                    </div>

					<div class="content-wrapper">

						<!-- BEGIN .composs-main-content -->
						<div class="composs-main-content composs-main-content-s-1">

							<div class="theiaStickySidebar">

								<!-- BEGIN .composs-panel -->
								<div class="composs-panel ms-tabs">

                                    <div class="composs-panel-title composs-panel-title-tabbed ms-tabs-index seo-strong" id="main-menu">
                                        <strong th:class="${paperPagePamData.module==''}? 'active' : ''">
                                            <a class="ms-link" th:href="@{'/search?q='+${encodeQ}+'&time_type='+ ${paperPagePamData.timeType}+'&sort_type=2'+'&search_type='+ ${paperPagePamData.searchType}+'&page=1'}" >全部</a>
                                        </strong>
                                        <strong th:class="${paperPagePamData.module=='tool_impact_factor'}? 'active' : ''">
                                            <!-- <a th:href="@{'/search?q='+${encodeQ}+'&module=tool_impact_factor'+'&time_type='+ ${paperPagePamData.timeType}+'&sort_type=2'+'&search_type='+ ${paperPagePamData.searchType}+'&page=1'}">期刊论坛</a> -->
                                            <a th:href="@{'/sci/index.do?fullname='+ ${encodeQ} +'&page=1'}">期刊论坛</a>
                                        </strong>
                                        <strong th:class="${paperPagePamData.module=='article'}? 'active' : ''">
                                            <a class="ms-link" th:href="@{'/search?q='+${encodeQ}+'&module=article'+'&time_type='+ ${paperPagePamData.timeType}+'&sort_type=2'+'&search_type='+ ${paperPagePamData.searchType}+'&page=1'}">资讯</a>
                                        </strong>
                                        <strong th:class="${paperPagePamData.module=='paper'}? 'active' : ''">
                                            <a class="ms-link" th:href="@{'/search?q='+${encodeQ}+'&module=paper'+'&time_type='+ ${paperPagePamData.timeType}+'&sort_type=2'+'&search_type='+ ${paperPagePamData.searchType}+'&page=1'}">文献</a>
                                        </strong>
                                        <strong th:class="${paperPagePamData.module=='eda'}? 'active' : ''">
                                            <a class="ms-link" th:href="@{'/search?q='+${encodeQ}+'&module=eda'+'&time_type='+ ${paperPagePamData.timeType}+'&sort_type=2'+ '&search_type='+ ${paperPagePamData.searchType}+'&page=1'}">医讯达</a>
                                        </strong>
                                        <strong th:class="${paperPagePamData.module=='scale'}? 'active' : ''">
                                            <a class="ms-link" th:href="@{'/search?q='+${encodeQ}+'&module=scale'+'&time_type='+ ${paperPagePamData.timeType}+'&sort_type=2'+'&search_type='+ ${paperPagePamData.searchType}+'&page=1'}">医学公式</a>
                                        </strong>
                                        <strong th:class="${paperPagePamData.module=='medsci_survey'}? 'active' : ''">
                                            <a class="ms-link" th:href="@{'/search?q='+${encodeQ}+'&module=medsci_survey'+'&time_type='+ ${paperPagePamData.timeType}+'&sort_type=2'+ '&search_type='+ ${paperPagePamData.searchType}+'&page=1'}">医调研</a>
                                        </strong>
                                    </div>

                                    <div style="margin-bottom: 30px;">
                                        <div class="clearfix">
                                            <p class="left common-resutl">为您找到相关结果约[[${totalSize}]]个</p>
                                            <p class="qikan-add">你是不是要搜索&nbsp;<span style="color: red;" th:text="'期刊'+${encodeW}"></span>
                                                <a th:href="@{'https://www.medsci.cn/sci/index.do?fullname='+ ${encodeW} +'&page=1'}">点击跳转</a>
                                            </p>
                                            <div class="select-area right">
                                                <select id="timeType" onchange="selectsType()" >
                                                    <option value="" th:selected="${paperPagePamData.timeType==0}">全部时间</option>
                                                    <!--<option value ="1">一天内</option>-->
                                                    <option value ="2" th:selected="${paperPagePamData.timeType==2}">一周内</option>
                                                    <option value="3"  th:selected="${paperPagePamData.timeType==3}">一月内</option>
                                                    <option value="4"  th:selected="${paperPagePamData.timeType==4}">一年内</option>
                                                </select>
                                                <select id="sortType" onchange="selectsType()">
                                                    <option value="2" th:selected="${paperPagePamData.sortType==2}">按发布时间排序</option>
                                                    <option value="1" th:selected="${paperPagePamData.sortType==1}">按相关度排序</option>
                                                </select>
                                                <select id="searchType" onchange="selectsType()">
                                                    <option value="1" th:selected="${paperPagePamData.searchType==1}">全文搜索</option>
                                                    <option value="2" th:selected="${paperPagePamData.searchType==2}">标题搜索</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="isJournalSearch"></div>

                                    <!-- 列表页面 -->
                                    <div class="composs-panel-inner">
                                        <div class="composs-blog-list lets-do-1">
                                            <div class="item" th:if="${not #strings.isEmpty(data.cover)}" th:each="data:${paperPageData}">
                                                <div class="item-header">
                                                    <!--资讯-->
                                                    <a target="_blank" th:if="${data.module=='article'}" th:href="'/article/show_article.do?id='+${data.moduleId}"><img thImg:src="@{${data.cover}(250,175)}" th:alt="${data.chTitle}" th:title="${data.chTitle}" /></a>
                                                    <!--指南-->
                                                    <a target="_blank" th:if="${data.module=='guider'}" th:href="'/guideline/show_article.do?id='+${data.moduleId}"><img thImg:src="@{${data.cover}(250,175)}" th:alt="${data.chTitle}" th:title="${data.chTitle}" /></a>
                                                    <!--期刊-->
                                                    <a target="_blank" th:if="${data.module=='tool_impact_factor'}" th:href="'/sci/submit.do?id='+${data.moduleId}"><img thImg:src="@{${data.cover}(250,175)}" th:alt="${data.chTitle}" th:title="${data.chTitle}" /></a>
                                                    <!--文献-->
                                                    <a target="_blank" th:if="${data.module=='paper'}" th:href="'/sci/show_paper.asp?id='+${data.moduleId}"><img thImg:src="@{${data.cover}(250,175)}" th:alt="${data.chTitle}" th:title="${data.chTitle}" /></a>
                                                    <!--医讯达eda-->
                                                    <a target="_blank" th:if="${data.module=='eda'}" th:href="'/eda/detail/'+${data.moduleId}"><img thImg:src="@{${data.cover}(250,175)}" th:alt="${data.chTitle}" th:title="${data.chTitle}" /></a>
                                                    <!--医学公式-->
                                                    <a target="_blank" th:if="${data.module=='scale'}" th:href="${scaleurl}+'/scale/show.do?id='+${data.moduleId}"><img thImg:src="@{${data.cover}(250,175)}" th:alt="${data.chTitle}" th:title="${data.chTitle}" /></a>
                                                    <!--医调研-->
                                                    <a target="_blank" th:if="${data.module=='medsci_survey'}" th:href="'/form/detail.do?id='+${data.moduleId}"><img thImg:src="@{${data.cover}(250,175)}" th:alt="${data.chTitle}" th:title="${data.chTitle}" /></a>
                                                </div>
                                                <div class="item-content">
                                                    <!--资讯-->
                                                    <h2 th:if="${data.module=='article'}"><a target="_blank" th:href="${baseurl}+'/article/show_article.do?id='+${data.moduleId}" class="ms-link ms-exposure" th:utext="${data.chTitle}"></a></h2>
                                                    <!--指南-->
                                                    <h2 th:if="${data.module=='guider'}"><a target="_blank" th:href="${baseurl}+'/guideline/show_article.do?id='+${data.moduleId}" class="ms-link ms-exposure" th:utext="${data.chTitle}"></a></h2>
                                                    <!--期刊-->
                                                    <h2 th:if="${data.module=='tool_impact_factor'}"><a target="_blank" th:href="${baseurl}+'/sci/submit.do?id='+${data.moduleId}" class="ms-link ms-exposure" th:utext="${data.chTitle}"></a></h2>
                                                    <!--文献-->
                                                    <h2 th:if="${data.module=='paper'}"><a target="_blank" th:href="'/sci/show_paper.asp?id='+${data.moduleId}" class="ms-link" th:utext="${data.chTitle}"></a></h2>
                                                    <!--医讯达eda-->
                                                    <h2 th:if="${data.module=='eda'}"><a target="_blank" th:href="${baseurl}+'/eda/detail/'+${data.moduleId}" class="ms-link ms-exposure" th:utext="${data.chTitle}"></a></h2>
                                                    <!--医学公式-->
                                                    <h2 th:if="${data.module=='scale'}"><a target="_blank" th:href="'https://m.medsci.cn/scale/show.do?id='+${data.moduleId}" class="ms-link" th:utext="${data.chTitle}"></a></h2>
                                                    <!--医调研-->
                                                    <h2 th:if="${data.module=='medsci_survey'}"><a target="_blank" th:href="'/form/detail.do?id='+${data.moduleId}" class="ms-link" th:utext="${data.chTitle}"></a></h2>
                                                    <p class="text-justify" th:utext="${data.summary}"></p>
                                                    <p style="color:rgba(117,117,117,.5); margin-top: 8px;">
                                                        [[${data.source}]]
                                                        <span th:if="${not #strings.isEmpty(data.source) && not #strings.isEmpty(data.tags)}">-</span>
                                                        [[${data.tags}]]
                                                        <span th:if="${(not #strings.isEmpty(data.tags) && not #strings.isEmpty(data.publishedTime)) || (not #strings.isEmpty(data.source) && not #strings.isEmpty(data.publishedTime))}">-</span>
                                                        [[${data.publishedTime}]]
                                                    </p>

                                                </div>
                                            </div>
                                            <div class="item item-no-img" th:if="${#strings.isEmpty(data.cover)}" th:each="data:${paperPageData}">
                                                <div class="item-content">
                                                    <!--资讯-->
                                                    <h2 th:if="${data.module=='article'}"><a target="_blank" th:href="'/article/show_article.do?id='+${data.moduleId}" class="ms-link" th:utext="${data.chTitle}"></a></h2>
                                                    <!--指南-->
                                                    <h2 th:if="${data.module=='guider'}"><a target="_blank" th:href="'/guideline/show_article.do?id='+${data.moduleId}" class="ms-link" th:utext="${data.chTitle}"></a></h2>
                                                    <!--期刊-->
                                                    <h2 th:if="${data.module=='tool_impact_factor'}"><a target="_blank" th:href="'/sci/submit.do?id='+${data.moduleId}" class="ms-link" th:utext="${data.chTitle}"></a></h2>
                                                    <!--文献-->
                                                    <h2 th:if="${data.module=='paper'}"><a target="_blank" th:href="'/sci/show_paper.asp?id='+${data.moduleId}" class="ms-link" th:utext="${data.chTitle}"></a></h2>
                                                    <!--医讯达eda-->
                                                    <h2 th:if="${data.module=='eda'}"><a target="_blank" th:href="'/eda/detail/'+${data.moduleId}" class="ms-link" th:utext="${data.chTitle}"></a></h2>
                                                    <!--医学公式-->
                                                    <h2 th:if="${data.module=='scale'}"><a target="_blank" th:href="'https://m.medsci.cn/scale/show.do?id='+${data.moduleId}" class="ms-link" th:utext="${data.chTitle}"></a></h2>
                                                    <!--医调研-->
                                                    <h2 th:if="${data.module=='medsci_survey'}"><a target="_blank" th:href="'/form/detail.do?id='+${data.moduleId}" class="ms-link" th:utext="${data.chTitle}"></a></h2>
                                                    <p class="text-justify" th:utext="${data.summary}"></p>
                                                    <p style="color:rgba(117,117,117,.5); margin-top: 8px;">
                                                        [[${data.source}]]
                                                        <span th:if="${not #strings.isEmpty(data.source) && not #strings.isEmpty(data.tags)}">-</span>
                                                        [[${data.tags}]]
                                                        <span th:if="${(not #strings.isEmpty(data.tags) && not #strings.isEmpty(data.publishedTime)) || (not #strings.isEmpty(data.source) && not #strings.isEmpty(data.publishedTime))}">-</span>
                                                        [[${data.publishedTime}]]
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <p class="qikan-result-mobile">为您找到相关结果约[[${totalSize}]]个</p>
                                    <div class="related-search related-search-mobile" style="display: none;">
                                        <strong>相关搜索</strong>
                                        <div id="relevantSearchDown">
                                            <div class="ot-shortcode-paragraph-row">
                                                <div class="column4" th:each="data,dataStat:${siteRelevantSearchData}" th:if="${dataStat.index<3}">
                                                    <p><a class="ms-link" style="cursor: pointer;" th:href="@{'/search?q='+${data.urlContent}+'&module='+ ${paperPagePamData.module}+'&time_type='+ ${paperPagePamData.timeType}+'&sort_type='+ ${paperPagePamData.sortType}+'&search_type='+ ${paperPagePamData.searchType}+'&page=1'}">[[${data.content}]]</a></p>
                                                </div>
                                            </div>
                                            <div class="ot-shortcode-paragraph-row">
                                                <div class="column4" th:each="data,dataStat:${siteRelevantSearchData}" th:if="${dataStat.index>=3 && dataStat.index<6}">
                                                    <p><a class="ms-link" style="cursor: pointer;" th:href="@{'/search?q='+${data.urlContent}+'&module='+ ${paperPagePamData.module}+'&time_type='+ ${paperPagePamData.timeType}+'&sort_type='+ ${paperPagePamData.sortType}+'&search_type='+ ${paperPagePamData.searchType}+'&page=1'}" >[[${data.content}]]</a></p>
                                                </div>
                                            </div>
                                            <div class="ot-shortcode-paragraph-row">
                                                <div class="column4" th:each="data,dataStat:${siteRelevantSearchData}" th:if="${dataStat.index>=6 && dataStat.index<9}">
                                                    <p><a class="ms-link" style="cursor: pointer;" th:href="@{'/search?q='+${data.urlContent}+'&module='+ ${paperPagePamData.module}+'&time_type='+ ${paperPagePamData.timeType}+'&sort_type='+ ${paperPagePamData.sortType}+'&search_type='+ ${paperPagePamData.searchType}+'&page=1'}" >[[${data.content}]]</a></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="composs-panel-pager related-search-mobile" th:utext="${PageTag}" th:data-total="${totalSize}">
                                    </div>
                                    <!--<div class="composs-panel-pager" style="text-align: left;" id="pageingArea"></div>-->
    
                                <!-- END .composs-panel -->
                                </div>
                                                        
							</div>

						<!-- END .composs-main-content -->
						</div>

						<!-- BEGIN #sidebar -->

<!--                        <p class="qikan-result-mshow">为您找到相关结果约[[${totalSize}]]个</p>-->
						<aside id="sidebar" class="index-sider">

                            <!-- BEGIN .widget -->
                            <div class="widget" style="display: none;">
                                <h3>相关搜索</h3>
                                <ul id="relevantSearch" class="menu">
                                    <li class="ms-link" style="cursor: pointer;" th:each="data:${siteRelevantSearchData}">
                                        <a th:href="@{'/search?q='+${data.urlContent}+'&module='+ ${paperPagePamData.module}+'&time_type='+ ${paperPagePamData.timeType}+'&sort_type='+ ${paperPagePamData.sortType}+'&search_type='+ ${paperPagePamData.searchType}+'&page=1'}" >[[${data.content}]]</a>
                                    </li>
                                </ul>
                                <div class="composs-panel-pager" th:utext="${PageTag}" th:data-total="${totalSize}">
                                </div>
                            <!-- END .widget -->
                            </div>

                            <!-- BEGIN .widget -->
                            <div class="widget">
                                <h3>相关推荐</h3>
                                <div class="recommend-icon-list clearfix">
                                    <a target="_blank" href="https://www.medsci.cn/m" style="cursor: pointer" class="item-area">
                                        <div class="icon-img"><img th:src="@{/img/medsci.png}" alt="" /></div>
                                        <div class="icon-font">梅斯医学</div>
                                    </a>
                                    <a target="_blank" href="https://www.medsci.cn/m" style="cursor: pointer" class="item-area">
                                        <div class="icon-img"><img th:src="@{/img/bioon.png}" alt="" /></div>
                                        <div class="icon-font">生物谷</div>
                                    </a>
                                </div>
                            <!-- END .widget -->
                            </div>
                            

                        <!-- END #sidebar -->
                        </aside>

					</div>

				<!-- END .wrapper -->
				</div>

			<!-- BEGIN .content -->
			</div>

            <!-- BEGIN #footer 页脚 -->
            <footer id="footer" th:insert="~{commonPages/footer::footer}"></footer>

            <!-- BEGIN 左侧悬浮 -->
            

            <!--移动端头部-->
            <div id="searchHead" th:insert="~{commonPages/webHeader::webHeader}"></div>
			
		<!-- END .boxed -->
		</div>

        <div th:insert="~{commonPages/web-side-menu::header}"></div>

        <script th:inline="javascript">
            var paperPagePamData=[[${paperPagePamData}]]
        </script>
        <script type="text/javascript" src="https://static.medsci.cn/product/medsci-site/portal/js/jquery-latest.min.js"></script>
        <script type="text/javascript" src="https://static.medsci.cn/product/medsci-site/portal/js/bootstrap.min.js"></script>
        <script type="text/javascript" src="https://static.medsci.cn/product/medsci-site/portal/js/theia-sticky-sidebar.min.js"></script>
        <script type="text/javascript" src="https://static.medsci.cn/product/medsci-site/portal/js/modernizr.js"></script>
        <script type="text/javascript" src="https://static.medsci.cn/product/medsci-site/portal/js/owl.carousel.min.js"></script>
        <!--<script type="text/javascript" th:src="@{/js/shortcode-scripts.js}"></script>-->
        <script type="text/javascript" src="https://static.medsci.cn/product/medsci-site/portal/js/ot-lightbox.min.js"></script>
        <script type="text/javascript" src="https://static.medsci.cn/product/medsci-site/portal/js/theme-scripts.min.js"></script>

        <script type="text/javascript" th:src="@{/js/ms-function.min.js}"></script>
        <script type="text/javascript" th:src="@{/js/util.js}"></script>
        <script type="text/javascript" th:src="@{/js/medsciSearch.js}"></script>

		<script>
            function getUrlParam(name) {
                //构造一个含有目标参数的正则表达式对象
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
                //匹配目标参数
                var r = window.location.search.substr(1).match(reg);
                if(r != null){
                    return decodeURIComponent(r[2]);
                }
                return null;//返回参数值
            }
            // 判断是期刊论坛时样式修改
            jQuery('.qikan-add').hide();
            if(getUrlParam('module') === 'tool_impact_factor' || getUrlParam('module') === null) {
                jQuery('.common-resutl').hide();
                jQuery('.qikan-add').show();
                jQuery('#searchContent .qikan-result-mobile').show();
            } else {
                jQuery('#searchContent .qikan-result-mobile').hide();
            }

			jQuery('.main-slider-owl').owlCarousel({
				margin: 20,
				responsiveClass: true,
				nav: true,
				dots: false,
				loop: true,
				autoplay: true,
				autoplayTimeout: 5000,
				autoplayHoverPause: true,
				responsive: {
					0: {
						items: 1
					},
					600: {
						items: 2
					}
				}
			});
		</script>
		<!-- Demo Only -->
		<!-- <script type="text/javascript" src="jscript/_ot-demo2.min.js"></script> -->

	<!-- END body -->
	</body>
<!-- END html -->
</html>